@extends('layouts.admin')

@section('title', 'Reservations Calendar - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Calendar</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Calendar</li>
                </ol>
            </nav>
        </div>
    </div>

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Calendar Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Reservations Calendar</div>
                    <div class="d-flex gap-2 align-items-center">
                        <!-- Field Filter -->
                        <select id="fieldFilter" class="form-select form-select-sm">
                            <option value="">All Fields</option>
                            @foreach ($fields as $field)
                                <option value="{{ $field->id }}">{{ $field->name }}</option>
                            @endforeach
                        </select>

                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Legend -->
                    <div class="calendar-legend mb-4 p-3 bg-light rounded">
                        <h6 class="fw-semibold mb-2">Legend:</h6>
                        <div class="d-flex flex-wrap gap-3 fs-12">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-warning me-2">&nbsp;</span>
                                <span>Pending</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-secondary me-2">&nbsp;</span>
                                <span>Confirmed</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">&nbsp;</span>
                                <span>Completed</span>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="calendarLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading calendar...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading calendar events...</p>
                    </div>

                    <!-- Calendar Container -->
                    <div id="calendar" style="display: none;"></div>

                    <!-- Error State -->
                    <div id="calendarError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Calendar Error</h6>
                        <p class="mb-0">Failed to load calendar events. Please refresh the page or contact support.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservation Details Modal -->
    <div class="modal fade effect-scale" id="reservationModal" tabindex="-1" aria-labelledby="reservationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reservationModalLabel">
                        <i class="ti ti-calendar-event me-2"></i>Reservation Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="reservationModalBody">
                    <!-- Loading state -->
                    <div id="modalLoading" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading reservation details...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading reservation details...</p>
                    </div>

                    <!-- Error state -->
                    <div id="modalError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Error Loading Reservation</h6>
                        <p class="mb-0">Failed to load reservation details. Please try again.</p>
                    </div>

                    <!-- Content will be loaded here -->
                    <div id="modalContent" class="d-none"></div>
                </div>
                <div class="modal-footer" id="reservationModalFooter">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- FullCalendar CSS and JS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>

    <!-- Custom FullCalendar Dark Mode Styles -->
    <style>
        /* FullCalendar Dark Mode Weekday Header Fix */
        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell .fc-col-header-cell-cushion {
            color: rgba(255,255,255,0.9) !important;
        }

        /* Additional dark mode calendar styling for consistency */
        [data-theme-mode="dark"] .fc-theme-standard .fc-scrollgrid {
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard td,
        [data-theme-mode="dark"] .fc-theme-standard th {
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day-number {
            color: rgba(255,255,255,0.8) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day.fc-day-today {
            background-color: rgba(var(--primary-rgb), 0.1) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary {
            background-color: rgb(var(--primary-rgb)) !important;
            border-color: rgb(var(--primary-rgb)) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary:hover {
            background-color: rgba(var(--primary-rgb), 0.9) !important;
            border-color: rgba(var(--primary-rgb), 0.9) !important;
        }

        [data-theme-mode="dark"] .fc .fc-toolbar-title {
            color: rgba(255,255,255,0.9) !important;
        }

        /* SPECIFIC FIX: Target .fc-scrollgrid-section-sticky class causing white background */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            background-image: none !important;
            background: rgb(var(--body-bg-rgb2)) !important;
        }

        /* Additional targeting for sticky header elements */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky .fc-col-header-cell,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky th,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky td {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255,255,255,0.1) !important;
            color: rgba(255,255,255,0.9) !important;
        }

        /* Legend dark mode styling */
        [data-theme-mode="dark"] .calendar-legend {
            background-color: rgb(var(--light-rgb)) !important;
            color: rgba(255,255,255,0.9) !important;
        }

        /* Ensure legend text is visible in dark mode */
        [data-theme-mode="dark"] .calendar-legend h6,
        [data-theme-mode="dark"] .calendar-legend span {
            color: rgba(255,255,255,0.9) !important;
        }

        /* Modal dark mode styling */
        [data-theme-mode="dark"] .modal-content {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] .modal-header {
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] .modal-footer {
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] .modal-title {
            color: rgba(255,255,255,0.9) !important;
        }

        /* Ensure modal cards are properly styled in dark mode */
        [data-theme-mode="dark"] #reservationModal .card {
            background-color: rgb(var(--light-rgb)) !important;
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] #reservationModal .card-header {
            background-color: rgb(var(--light-rgb)) !important;
            border-color: rgba(255,255,255,0.1) !important;
        }

        [data-theme-mode="dark"] #reservationModal .table {
            color: rgba(255,255,255,0.9) !important;
        }

        [data-theme-mode="dark"] #reservationModal .table-light {
            background-color: rgba(255,255,255,0.1) !important;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');
            const fieldFilter = document.getElementById('fieldFilter');

            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                timeZone: 'local',
                eventDisplay: 'block',
                displayEventTime: true,
                weekNumbers: true,
                weekNumberCalculation: 'ISO',
                navLinks: true,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                editable: true,
                selectable: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    const fieldId = fieldFilter.value;
                    const url = new URL('{{ route('calendar.events') }}');
                    url.searchParams.append('start', fetchInfo.startStr);
                    url.searchParams.append('end', fetchInfo.endStr);
                    if (fieldId) {
                        url.searchParams.append('field_id', fieldId);
                    }

                    console.log('Fetching calendar events from:', url.toString());

                    fetch(url)
                        .then(response => {
                            console.log('Calendar events response status:',
                                response.status);
                            if (!response.ok) {
                                throw new Error(
                                    `HTTP error! status: ${response.status}`
                                );
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events data received:', data);
                            document.getElementById('calendarLoading').style
                                .display = 'none';
                            document.getElementById('calendar').style.display =
                                'block';
                            document.getElementById('calendarError').classList
                                .add('d-none');
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Calendar events fetch error:',
                                error);
                            document.getElementById('calendarLoading').style
                                .display = 'none';
                            document.getElementById('calendarError').classList
                                .remove('d-none');
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    // Prevent default navigation
                    info.jsEvent.preventDefault();

                    // Open reservation details in modal
                    openReservationModal(info.event.extendedProps.reservation_id);
                },
                dateClick: function(info) {
                    // Get the current view type
                    const currentView = calendar.view.type;

                    // Month view behavior: navigate to day view for the clicked date
                    if (currentView === 'dayGridMonth') {
                        // Switch to day view for the clicked date
                        calendar.changeView('timeGridDay', info.dateStr);
                    }
                    // Week/Day view behavior: open reservation creation screen
                    else if (currentView === 'timeGridWeek' || currentView === 'timeGridDay') {
                        // Redirect to reservation creation page with date/time parameters
                        let url = '/reservations/create?date=' + encodeURIComponent(info.dateStr);

                        // If time is included (time grid views), also pass time separately
                        if (info.dateStr.includes('T')) {
                            const [date, time] = info.dateStr.split('T');
                            url = `/reservations/create?date=${encodeURIComponent(date)}&time=${encodeURIComponent(time)}`;
                        }

                        window.location.href = url;
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip with reservation details
                    const props = info.event.extendedProps;
                    info.el.title =
                        `${props.field_name}\nCustomer: ${props.customer_name}\nStatus: ${props.status}\nCost: $${props.total_cost}\nDuration: ${props.duration} hours`;
                },
                eventDrop: function(info) {
                    const event = info.event;

                    fetch(`/calendar/update-reservation/${event.id}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content')
                            },
                            body: JSON.stringify({
                                start: event.start.toISOString(),
                                end: event.end ? event.end.toISOString() : null
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to update reservation.');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log(data.message);
                        })
                        .catch(error => {
                            alert('Error updating reservation.');
                            info.revert(); // undo the drag
                        });
                }
            });

            // Show loading state initially
            document.getElementById('calendarLoading').style.display = 'block';
            document.getElementById(
                'calendar').style.display = 'none';

            calendar.render();

            // Initial load complete
            setTimeout(() => {
                document.getElementById('calendarLoading').style.display = 'none';
                document.getElementById('calendar').style.display = 'block';
            }, 1000);

            // Refresh calendar when field filter changes
            fieldFilter.addEventListener('change', function() {
                document.getElementById('calendarLoading').style.display = 'block';
                document.getElementById('calendar').style.display = 'none';
                calendar.refetchEvents();
            });
        });

        // Modal functions
        function openReservationModal(reservationId) {
            const modal = new bootstrap.Modal(document.getElementById('reservationModal'));
            const modalBody = document.getElementById('reservationModalBody');
            const modalLoading = document.getElementById('modalLoading');
            const modalError = document.getElementById('modalError');
            const modalContent = document.getElementById('modalContent');
            const modalFooter = document.getElementById('reservationModalFooter');

            // Reset modal state
            modalLoading.style.display = 'block';
            modalError.classList.add('d-none');
            modalContent.classList.add('d-none');

            // Show modal
            modal.show();

            // Fetch reservation details
            fetch(`/reservations/${reservationId}/details`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                modalLoading.style.display = 'none';
                modalContent.classList.remove('d-none');
                renderReservationDetails(data);
                updateModalFooter(data);
            })
            .catch(error => {
                console.error('Error fetching reservation details:', error);
                modalLoading.style.display = 'none';
                modalError.classList.remove('d-none');
            });
        }

        function renderReservationDetails(reservation) {
            const modalContent = document.getElementById('modalContent');
            const modalTitle = document.getElementById('reservationModalLabel');

            // Update modal title
            modalTitle.innerHTML = `<i class="ti ti-calendar-event me-2"></i>Reservation #${reservation.id}`;

            // Build the content HTML
            const contentHtml = `
                <div class="row gy-4">
                    <!-- Field Information -->
                    <div class="col-xl-6">
                        <div class="card custom-card shadow-none border">
                            <div class="card-header">
                                <div class="card-title">Field Information</div>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="me-3">
                                        <span class="avatar avatar-lg bg-primary-transparent">
                                            <i class="ti ti-building-stadium fs-20"></i>
                                        </span>
                                    </div>
                                    <div>
                                        <h5 class="mb-1">${reservation.field.name}</h5>
                                        <span class="badge bg-info-transparent">${reservation.field.type}</span>
                                        <span class="badge bg-${reservation.status_color}-transparent text-${reservation.status_color} ms-2">
                                            ${reservation.status}
                                        </span>
                                    </div>
                                </div>

                                <div class="row gy-2">
                                    <div class="col-4">
                                        <small class="text-muted">Capacity</small>
                                        <div class="fw-semibold">${reservation.field.capacity} people</div>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">Day Hourly Rate</small>
                                        <div class="fw-semibold">XCG ${parseFloat(reservation.field.hourly_rate).toFixed(2)}</div>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">Night Hourly Rate</small>
                                        <div class="fw-semibold">XCG ${parseFloat(reservation.field.night_hourly_rate).toFixed(2)}</div>
                                    </div>
                                    <div class="col-12">
                                        <small class="text-muted">Description</small>
                                        <div class="fw-semibold">${reservation.field.description}</div>
                                    </div>
                                    ${reservation.utilities.length > 0 ? `
                                        <div class="col-12 mt-3">
                                            <small class="text-muted">Utilities</small>
                                            <div class="table-responsive mt-2">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>Utility</th>
                                                            <th>Quantity</th>
                                                            <th>Rate</th>
                                                            <th>Total Cost</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        ${reservation.utilities.map(utility => `
                                                            <tr>
                                                                <td>${utility.name}</td>
                                                                <td>${utility.hours}</td>
                                                                <td>XCG ${parseFloat(utility.rate).toFixed(2)}</td>
                                                                <td>XCG ${parseFloat(utility.cost).toFixed(2)}</td>
                                                            </tr>
                                                        `).join('')}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reservation Schedule -->
                    <div class="col-xl-6">
                        <div class="card custom-card shadow-none border">
                            <div class="card-header">
                                <div class="card-title">Schedule & Cost</div>
                            </div>
                            <div class="card-body">
                                <div class="row gy-3">
                                    <div class="col-12">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-calendar me-2 text-primary"></i>
                                            <div>
                                                <small class="text-muted">Date</small>
                                                <div class="fw-semibold">${reservation.booking_date}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-clock me-2 text-info"></i>
                                            <div>
                                                <small class="text-muted">Time</small>
                                                <div class="fw-semibold">${reservation.time_range}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-hourglass me-2 text-warning"></i>
                                            <div>
                                                <small class="text-muted">Duration</small>
                                                <div class="fw-semibold">${reservation.duration_hours} ${reservation.duration_hours === 1 ? 'hour' : 'hours'}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        ${renderCostBreakdown(reservation)}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="col-xl-12">
                        <div class="card custom-card shadow-none border">
                            <div class="card-header">
                                <div class="card-title">Customer Information</div>
                            </div>
                            <div class="card-body">
                                <div class="row gy-3">
                                    <div class="col-md-4">
                                        <small class="text-muted">Customer Name</small>
                                        <div class="fw-semibold">${reservation.customer_display_name}</div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Email</small>
                                        <div class="fw-semibold">${reservation.customer_email}</div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Phone</small>
                                        <div class="fw-semibold">${reservation.customer_phone}</div>
                                    </div>
                                    ${reservation.special_requests ? `
                                        <div class="col-12">
                                            <small class="text-muted">Special Requests</small>
                                            <div class="fw-semibold">${reservation.special_requests}</div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            modalContent.innerHTML = contentHtml;
        }

        function renderCostBreakdown(reservation) {
            const costBreakdown = reservation.cost_breakdown;
            let html = `
                <div class="alert alert-success">
                    <h6 class="fw-semibold mb-3">Reservation Cost Breakdown</h6>

                    <!-- Field Cost Breakdown -->
                    <div class="mb-3">
                        <div class="fw-semibold mb-2">Field Cost:</div>
            `;

            // Check if we have rate breakdown data
            if (costBreakdown.rate_breakdown &&
                (costBreakdown.rate_breakdown.day_hours > 0 || costBreakdown.rate_breakdown.night_hours > 0)) {

                if (costBreakdown.rate_breakdown.day_hours > 0) {
                    html += `
                        <div class="fs-12 text-muted">
                            Day Rate: ${costBreakdown.rate_breakdown.day_hours} hours × XCG ${parseFloat(reservation.field.hourly_rate).toFixed(2)} = XCG ${parseFloat(costBreakdown.rate_breakdown.day_cost).toFixed(2)}
                        </div>
                    `;
                }

                if (costBreakdown.rate_breakdown.night_hours > 0) {
                    html += `
                        <div class="fs-12 text-muted">
                            Night Rate: ${costBreakdown.rate_breakdown.night_hours} hours × XCG ${parseFloat(reservation.field.night_hourly_rate).toFixed(2)} = XCG ${parseFloat(costBreakdown.rate_breakdown.night_cost).toFixed(2)}
                        </div>
                    `;
                }
            } else {
                // Simple rate display
                html += `
                    <div class="fs-12 text-muted">
                        ${reservation.duration_hours} hours × XCG ${parseFloat(reservation.field.hourly_rate).toFixed(2)} = XCG ${parseFloat(costBreakdown.subtotal).toFixed(2)}
                    </div>
                `;
            }

            html += `
                        <div class="fs-13 fw-semibold mt-1">
                            Field Total: XCG ${parseFloat(costBreakdown.subtotal).toFixed(2)}
                        </div>
                    </div>
            `;

            // Utility Cost Breakdown
            if (reservation.utilities.length > 0) {
                html += `
                    <div class="mb-3">
                        <div class="fw-semibold mb-2">Utility Costs:</div>
                `;

                reservation.utilities.forEach(utility => {
                    html += `
                        <div class="fs-12 text-muted">
                            ${utility.name}: ${utility.hours} hours × XCG ${parseFloat(utility.rate).toFixed(2)} = XCG ${parseFloat(utility.cost).toFixed(2)}
                        </div>
                    `;
                });

                html += `
                        <div class="fs-13 fw-semibold mt-1">
                            Utility Total: XCG ${parseFloat(reservation.utility_total).toFixed(2)}
                        </div>
                    </div>
                `;
            }

            // Total Cost
            html += `
                    <!-- Total Cost -->
                    <div class="border-top pt-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="fw-bold fs-16">Total Cost:</span>
                                <div class="fs-11 text-muted mt-1">
                                    <i class="ti ti-shield-check me-1"></i>Calculated securely by server
                                </div>
                            </div>
                            <div class="h4 mb-0 text-success">
                                XCG ${parseFloat(reservation.total_cost).toFixed(2)}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return html;
        }

        function updateModalFooter(reservation) {
            const modalFooter = document.getElementById('reservationModalFooter');

            let footerHtml = `
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>Close
                </button>
            `;

            // Add action buttons if user can modify/cancel
            if (reservation.can_be_modified) {
                footerHtml = `
                    <a href="${reservation.edit_url}" class="btn btn-warning">
                        <i class="ti ti-edit me-1"></i>Edit Reservation
                    </a>
                ` + footerHtml;
            }

            if (reservation.can_be_cancelled) {
                footerHtml = `
                    <button type="button" class="btn btn-danger" onclick="cancelReservation('${reservation.cancel_url}')">
                        <i class="ti ti-x me-1"></i>Cancel Reservation
                    </button>
                ` + footerHtml;
            }

            modalFooter.innerHTML = footerHtml;
        }

        function cancelReservation(cancelUrl) {
            if (confirm('Are you sure you want to cancel this reservation?')) {
                // Create a form and submit it
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = cancelUrl;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                form.appendChild(csrfToken);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection
